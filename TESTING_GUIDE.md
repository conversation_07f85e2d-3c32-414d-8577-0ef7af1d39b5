# Saudi Arabian Receipt Template - Testing Guide

## Prerequisites

1. **Odoo 17 Installation**: Ensure you have Odoo 17 running
2. **Point of Sale Module**: Install and configure the Point of Sale module
3. **Module Installation**: Install this custom module

## Installation Steps

1. **Copy Module**: Place the module in your Odoo addons directory
2. **Update Apps List**: Go to Apps → Update Apps List
3. **Install Module**: Search for "Point of Sale Custom Receipt Reset" and install
4. **Restart Odoo**: Restart the Odoo server to ensure all assets are loaded
5. **Clear Browser Cache**: Clear your browser cache completely

## Testing Checklist

### ✅ Module Loading Test
- [ ] Modu<PERSON> appears in Apps list
- [ ] Module installs without errors
- [ ] No errors in Odoo logs during installation

### ✅ POS Interface Test
- [ ] Open Point of Sale application
- [ ] Interface loads without blank screens
- [ ] No JavaScript errors in browser console (F12)
- [ ] Discount button (5%) appears in product screen
- [ ] Last Orders button appears in product screen

### ✅ Receipt Template Test
- [ ] Create a test order with multiple products
- [ ] Add a customer to the order
- [ ] Apply some discounts
- [ ] Complete the payment
- [ ] Click "Print Receipt" or "Validate"
- [ ] Receipt displays without blank screen

### ✅ Saudi Arabian Format Validation
- [ ] **Header Section**:
  - [ ] Company logo displays correctly
  - [ ] Business name in Arabic and English
  - [ ] Complete address information
  - [ ] CR and VAT numbers (if configured)

- [ ] **Invoice Information**:
  - [ ] "فاتورة ضريبية مبسطة / Simplified Tax Invoice" header
  - [ ] Invoice number with Arabic/English labels
  - [ ] Date and time with Arabic/English labels
  - [ ] Cashier information (if available)

- [ ] **Customer Section** (when customer is selected):
  - [ ] Customer name with Arabic/English labels
  - [ ] Customer VAT (if available)

- [ ] **Product Table**:
  - [ ] Bilingual column headers (Arabic/English)
  - [ ] Product names display correctly
  - [ ] Unit prices with "ر.س" currency symbol
  - [ ] Quantities and totals
  - [ ] Customer notes (if any)
  - [ ] Lot/Serial numbers (if applicable)

- [ ] **Totals Section**:
  - [ ] Subtotal with Arabic/English labels
  - [ ] Discounts (if applicable)
  - [ ] VAT 15% calculation
  - [ ] Grand total prominently displayed
  - [ ] Total quantity summary
  - [ ] Savings amount (if applicable)

- [ ] **Payment Section**:
  - [ ] Payment methods with Arabic/English labels
  - [ ] Cash/Card detection and labeling
  - [ ] Change amount (if applicable)

- [ ] **Footer Section**:
  - [ ] QR code (if configured)
  - [ ] Thank you message in Arabic/English
  - [ ] Store contact information

### ✅ Currency and Language Test
- [ ] All amounts display with "ر.س" (Saudi Riyal) symbol
- [ ] Arabic text displays correctly (RTL direction)
- [ ] English text displays correctly (LTR direction)
- [ ] Mixed Arabic/English content aligns properly

### ✅ Thermal Printer Compatibility
- [ ] Receipt width fits 58mm thermal printers
- [ ] Receipt width fits 80mm thermal printers
- [ ] Font sizes are appropriate for thermal printing
- [ ] Line spacing is optimal for thermal printers
- [ ] Print preview shows correct formatting

## Troubleshooting

### Issue: Blank Screen on Validate
**Cause**: JavaScript errors or template conflicts
**Solution**:
1. Check browser console for errors
2. Clear browser cache completely
3. Restart Odoo server
4. Verify module is properly installed

### Issue: Default Receipt Still Shows
**Cause**: Template not loading or inheritance issues
**Solution**:
1. Verify files are in correct locations
2. Check __manifest__.py assets configuration
3. Restart Odoo server
4. Update the module (Apps → Upgrade)

### Issue: Arabic Text Not Displaying
**Cause**: Font or encoding issues
**Solution**:
1. Ensure browser supports Arabic fonts
2. Check CSS font-family declarations
3. Verify XML encoding is UTF-8

### Issue: Currency Symbols Missing
**Cause**: CSS not loading or font issues
**Solution**:
1. Check if CSS files are loaded in browser
2. Verify font supports Arabic currency symbols
3. Clear browser cache

## Browser Console Debugging

Open browser developer tools (F12) and check for:
- JavaScript errors in Console tab
- Failed resource loading in Network tab
- Template rendering issues in Elements tab

## Log File Locations

Check Odoo logs for errors:
- Ubuntu/Debian: `/var/log/odoo/odoo-server.log`
- Custom installation: Check your Odoo configuration file

## Support

If issues persist:
1. Check Odoo version compatibility (requires Odoo 17)
2. Verify Point of Sale module is properly configured
3. Test with a fresh database
4. Review module dependencies
