#!/usr/bin/env python3
"""
Saudi Arabian Receipt Header Template - Validation Test
Tests the implementation of the Saudi thermal receipt header format.
"""

import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

def test_file_structure():
    """Test that all required files exist and are properly structured."""
    print("🔍 Testing file structure...")
    
    required_files = [
        "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml",
        "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml",
        "static/src/css/custom_pos_receipts.css",
        "__manifest__.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files exist")
    return True

def test_receipt_header_template():
    """Test the Saudi receipt header template structure."""
    print("🔍 Testing receipt header template...")
    
    template_path = "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml"
    
    try:
        tree = ET.parse(template_path)
        root = tree.getroot()
        
        # Check for Saudi-specific elements
        saudi_elements = [
            ".//div[@class='sa-logo-section']",
            ".//div[@class='sa-business-name-section']",
            ".//div[@class='sa-business-info-section']",
            ".//div[@class='sa-registration-section']",
            ".//div[@class='sa-contact-section']"
        ]
        
        missing_elements = []
        for element_xpath in saudi_elements:
            if not root.find(element_xpath):
                missing_elements.append(element_xpath)
        
        if missing_elements:
            print(f"❌ Missing Saudi elements: {missing_elements}")
            return False
        
        # Check for Arabic text content
        arabic_labels = [
            "السجل التجاري",  # Commercial Registration
            "الرقم الضريبي",   # VAT Registration
            "هاتف:",          # Phone
            "الكاشير:",       # Cashier
            "رقم التتبع:"     # Tracking
        ]
        
        template_content = ET.tostring(root, encoding='unicode')
        missing_arabic = []
        for arabic_text in arabic_labels:
            if arabic_text not in template_content:
                missing_arabic.append(arabic_text)
        
        if missing_arabic:
            print(f"❌ Missing Arabic labels: {missing_arabic}")
            return False
        
        print("✅ Receipt header template structure is correct")
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML parsing error in receipt header: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing receipt header: {e}")
        return False

def test_order_receipt_template():
    """Test the Saudi order receipt template structure."""
    print("🔍 Testing order receipt template...")
    
    template_path = "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml"
    
    try:
        tree = ET.parse(template_path)
        root = tree.getroot()
        
        # Check for Saudi-specific elements
        saudi_elements = [
            ".//div[@class='sa-invoice-type-section']",
            ".//div[@class='sa-invoice-info-section']",
            ".//div[@class='sa-products-header']",
            ".//div[@class='sa-total-section']",
            ".//div[@class='sa-footer-section']"
        ]
        
        missing_elements = []
        for element_xpath in saudi_elements:
            if not root.find(element_xpath):
                missing_elements.append(element_xpath)
        
        if missing_elements:
            print(f"❌ Missing Saudi order elements: {missing_elements}")
            return False
        
        # Check for Arabic invoice content
        arabic_content = [
            "فاتورة ضريبية مبسطة",  # Simplified Tax Invoice
            "رقم الفاتورة",         # Invoice No.
            "التاريخ",             # Date
            "العميل",              # Customer
            "شكراً لزيارتكم"       # Thank you for your visit
        ]
        
        template_content = ET.tostring(root, encoding='unicode')
        missing_content = []
        for arabic_text in arabic_content:
            if arabic_text not in template_content:
                missing_content.append(arabic_text)
        
        if missing_content:
            print(f"❌ Missing Arabic content: {missing_content}")
            return False
        
        print("✅ Order receipt template structure is correct")
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML parsing error in order receipt: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing order receipt: {e}")
        return False

def test_css_styles():
    """Test that Saudi CSS styles are properly defined."""
    print("🔍 Testing CSS styles...")
    
    css_path = "static/src/css/custom_pos_receipts.css"
    
    try:
        with open(css_path, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # Check for Saudi-specific CSS classes
        required_classes = [
            ".sa-thermal-receipt",
            ".sa-logo-section",
            ".sa-business-name-ar",
            ".sa-business-name-en",
            ".sa-registration-section",
            ".sa-cr-number",
            ".sa-vat-number",
            ".sa-label-ar",
            ".sa-label-en",
            ".sa-contact-section",
            ".sa-invoice-type-section",
            ".sa-total-section",
            ".sa-footer-section"
        ]
        
        missing_classes = []
        for css_class in required_classes:
            if css_class not in css_content:
                missing_classes.append(css_class)
        
        if missing_classes:
            print(f"❌ Missing CSS classes: {missing_classes}")
            return False
        
        # Check for thermal printer optimizations
        thermal_features = [
            "58mm",
            "80mm",
            "thermal",
            "384px",  # 58mm width
            "576px"   # 80mm width
        ]
        
        missing_features = []
        for feature in thermal_features:
            if feature not in css_content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ Missing thermal printer features: {missing_features}")
            return False
        
        print("✅ CSS styles are properly defined")
        return True
        
    except Exception as e:
        print(f"❌ Error testing CSS: {e}")
        return False

def test_manifest_configuration():
    """Test that the manifest properly loads all assets."""
    print("🔍 Testing manifest configuration...")
    
    try:
        with open("__manifest__.py", 'r', encoding='utf-8') as f:
            manifest_content = f.read()
        
        # Check for required asset paths
        required_assets = [
            "static/src/css/custom_pos_receipts.css",
            "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml",
            "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml"
        ]
        
        missing_assets = []
        for asset in required_assets:
            if asset not in manifest_content:
                missing_assets.append(asset)
        
        if missing_assets:
            print(f"❌ Missing assets in manifest: {missing_assets}")
            return False
        
        # Check for proper dependencies
        if "'point_of_sale'" not in manifest_content:
            print("❌ Missing point_of_sale dependency")
            return False
        
        print("✅ Manifest configuration is correct")
        return True
        
    except Exception as e:
        print(f"❌ Error testing manifest: {e}")
        return False

def main():
    """Run all tests for the Saudi receipt header implementation."""
    print("🇸🇦 Saudi Arabian Receipt Header Template - Validation Test")
    print("=" * 60)
    
    tests = [
        test_file_structure,
        test_receipt_header_template,
        test_order_receipt_template,
        test_css_styles,
        test_manifest_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Saudi receipt header is ready for use.")
        print("\n📋 Next Steps:")
        print("1. Install/upgrade the module in Odoo")
        print("2. Test receipt printing in Point of Sale")
        print("3. Verify Saudi format on thermal printers")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
