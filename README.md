# Point of Sale Custom Receipt Reset - Saudi Arabian Thermal Receipt Format

This Odoo 17 addon provides a complete Saudi Arabian thermal receipt template for the Point of Sale application, designed to meet local business requirements and thermal printer specifications.

## Features

### 🇸🇦 **Saudi Arabian Compliance**
- **Bilingual Layout**: Complete Arabic/English bilingual receipt format
- **VAT Compliance**: Proper VAT 15% calculation and display
- **Business Registration**: Commercial Registration (CR) and VAT number display
- **Simplified Tax Invoice**: Compliant invoice type labeling

### 🖨️ **Thermal Printer Optimization**
- **58mm/80mm Support**: Responsive design for both thermal printer widths
- **Font Optimization**: Proper font sizing for thermal printing (10px-16px range)
- **Print-Ready CSS**: Optimized CSS with @media print queries
- **RTL/LTR Support**: Proper text direction handling for Arabic and English content

### 📋 **Complete Receipt Structure**
- **Enhanced Header**: Company logo, business name, address, CR/VAT numbers
- **Invoice Information**: Invoice type, number, date/time, cashier details
- **Customer Section**: Customer information (when available)
- **Product Table**: Bilingual column headers with Arabic product name support
- **Comprehensive Totals**: Subtotal, discount, VAT, grand total, quantity summary
- **Payment Methods**: Cash/card payment display with Arabic labels
- **Professional Footer**: QR code, thank you message, contact information

## Installation

1. **Copy the addon** to your Odoo custom addons directory:
   ```bash
   cp -r point_of_sale_customization /path/to/odoo/custom_addons/
   ```

2. **Activate Developer Mode** in Odoo:
   - Go to Settings → Activate Developer Mode

3. **Update Apps List**:
   - Go to Apps → Update Apps List

4. **Install the addon**:
   - Search for "Point of Sale Custom Receipt Reset"
   - Click Install

## Usage

Once installed, the Saudi Arabian thermal receipt template will automatically be applied to all Point of Sale receipts. The receipt includes:

### 📄 **Receipt Sections (Top to Bottom)**

**Header Section:**
- Company logo (centered, scalable for thermal printers)
- Business name in Arabic and English (bold, largest font)
- Complete business address (Arabic primary, English secondary)
- Commercial Registration number with bilingual labels
- VAT Registration number with bilingual labels

**Invoice Information:**
- "فاتورة ضريبية مبسطة / Simplified Tax Invoice" (centered, bold)
- Invoice number with Arabic/English labels
- Date and time with Arabic/English labels
- Cashier name (if available)

**Customer Information (conditional):**
- Customer name and details (only if customer data exists)
- Customer VAT number (if available)

**Product Table:**
- Bilingual column headers (Arabic primary, English secondary)
- Product names with Arabic support
- Unit price, quantity, and total columns
- Customer notes and lot/serial numbers
- Subtotal with separator line

**Totals Section:**
- Subtotal, discount (if applicable), total before VAT
- VAT 15% calculation with bilingual labels
- Grand total (highlighted, larger font)
- Total quantity count

**Payment Section:**
- Payment methods (Cash/Card) with Arabic labels
- Change amount (if applicable)

**Footer Section:**
- QR code with bilingual scan instructions
- Thank you message in Arabic and English
- Store contact information

## Customization

To further customize the receipt templates:

1. **Modify Templates**: Edit the XML files in `static/src/app/screens/receipt_screen/receipt/`
2. **Update Styling**: Modify the CSS file in `static/src/css/custom_pos_receipts.css`
3. **Add New Elements**: Use XPath expressions to add new sections to the receipt

### 🏗️ **Template Structure**

- **Main Receipt**: `custom_order_receipt.xml` - Complete Saudi Arabian receipt format
- **Receipt Header**: `custom_receipt_header.xml` - Saudi business header with CR/VAT info
- **Thermal CSS**: `custom_pos_receipts.css` - Optimized for 58mm/80mm thermal printers

### 🎨 **CSS Classes for Customization**

**Main Container:**
- `.sa-thermal-receipt` - Main receipt container
- `.sa-logo-section` - Company logo section
- `.sa-business-name-section` - Business name area

**Content Sections:**
- `.sa-invoice-info-section` - Invoice information
- `.sa-product-table-section` - Product table
- `.sa-totals-section` - Totals and VAT
- `.sa-payment-section` - Payment methods
- `.sa-footer-section` - Footer with QR code

**Typography Classes:**
- `.sa-label-ar` - Arabic labels
- `.sa-label-en` - English labels
- `.sa-amount` - Monetary amounts
- `.sa-currency` - Currency symbols

### Inheritance Pattern

The addon uses Odoo's template inheritance system:

```xml
<t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
    <xpath expr="//target-element" position="replace|before|after|inside">
        <!-- Custom content -->
    </xpath>
</t>
```

## Technical Details

- **Odoo Version**: 17.0
- **Dependencies**: point_of_sale
- **License**: LGPL-3

## File Structure

```
point_of_sale_custom_reset/
├── __init__.py
├── __manifest__.py
├── README.md
└── static/
    └── src/
        ├── app/
        │   └── screens/
        │       └── receipt_screen/
        │           └── receipt/
        │               ├── custom_order_receipt.xml
        │               └── receipt_header/
        │                   └── custom_receipt_header.xml
        └── css/
            └── custom_pos_receipts.css
```

## Support

For support and customization requests, please contact your development team.

## Contributing

When making changes to this addon:

1. Test thoroughly in a development environment
2. Ensure compatibility with existing PoS functionality
3. Follow Odoo development best practices
4. Document any new features or changes

## License

This addon is licensed under LGPL-3.
