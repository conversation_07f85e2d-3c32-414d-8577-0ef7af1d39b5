# -*- coding: utf-8 -*-
{
    'name': 'Point of Sale Custom Receipt Reset',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Custom receipt template modifications for Point of Sale',
    'description': """
        This module provides custom receipt template modifications for the Point of Sale application.
        It uses proper inheritance patterns to extend the existing PoS receipt templates without
        modifying core files.
        
        Features:
        - Custom receipt header layout
        - Modified receipt formatting
        - Enhanced receipt styling
        - Proper template inheritance
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'point_of_sale',
    ],
    'data': [
        # Views and templates will be added here
    ],
    # 'assets': {
    #     'point_of_sale._assets_pos': [
    #         'point_of_sale_customization/static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml',
    #         'point_of_sale_customization/static/src/app/screens/receipt_screen/receipt/receipt_header/custom_receipt_header.xml',
    #         'point_of_sale_customization/static/src/css/custom_pos_receipts.css',
    #     ],
    # },
    'assets': {
        'point_of_sale._assets_pos': [
            'point_of_sale_customization/static/src/css/custom_pos_receipts.css',
            'point_of_sale_customization/static/src/scss/point_of_sale_customization.scss',
            'point_of_sale_customization/static/src/xml/point_of_sale_customization.xml',
            'point_of_sale_customization/static/src/js/point_of_sale_customization.js'
            # 'point_of_sale_customization/static/src/js/point_of_sale_customization2.js'
        ],
    },

    'installable': True,
    'auto_install': False,
    'application': False,
}
