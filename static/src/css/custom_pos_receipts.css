/* Saudi Arabian Thermal Receipt Styles - Optimized for 58mm/80mm Printers */

/* Base Thermal Receipt Container */
.sa-thermal-receipt {
    font-family: '<PERSON>hom<PERSON>', 'Arial Unicode MS', 'DejaVu Sans', sans-serif;
    direction: ltr; /* Base direction for mixed content */
    text-align: center;
    line-height: 1.2;
    font-size: 12px; /* Base font size for thermal printers */
    color: #000;
    background: #fff;
    width: 100%;
    max-width: 384px; /* 58mm thermal printer width */
    margin: 0 auto;
    padding: 8px;
    box-sizing: border-box;
}

/* 80mm thermal printer support */
@media (min-width: 500px) {
    .sa-thermal-receipt {
        max-width: 576px; /* 80mm thermal printer width */
        padding: 12px;
    }
}

/* ===== HEADER SECTION STYLES ===== */

/* Company Logo */
.sa-logo-section {
    text-align: center;
    margin-bottom: 12px;
}

.sa-company-logo {
    width: 80px;
    max-width: 120px;
    height: auto;
    display: block;
    margin: 0 auto;
}

/* 80mm printer logo sizing */
@media (min-width: 500px) {
    .sa-company-logo {
        width: 100px;
        max-width: 150px;
    }
}

/* Business Name Section */
.sa-business-name-section {
    margin: 8px 0;
}

.sa-business-name-ar {
    font-size: 16px;
    font-weight: bold;
    color: #000;
    direction: rtl;
    text-align: center;
    margin: 2px 0;
}

.sa-business-name-en {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    direction: ltr;
    text-align: center;
    margin: 2px 0;
}

/* Business Address Section */
.sa-address-section {
    margin: 6px 0;
    font-size: 10px;
}

.sa-address-ar {
    direction: rtl;
    text-align: center;
    color: #333;
    margin: 1px 0;
}

.sa-address-en {
    direction: ltr;
    text-align: center;
    color: #666;
    font-size: 9px;
    margin: 1px 0;
}

/* Registration Section */
.sa-registration-section {
    margin: 8px 0;
    font-size: 10px;
}

.sa-cr-number, .sa-vat-number {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2px 0;
    padding: 1px 0;
}

.sa-label-ar {
    font-weight: bold;
    color: #000;
    direction: rtl;
    font-size: 10px;
}

.sa-label-en {
    font-weight: normal;
    color: #666;
    direction: ltr;
    font-size: 9px;
}

.sa-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #000;
    direction: ltr;
}

/* Contact Section */
.sa-contact-section {
    margin: 6px 0;
    font-size: 9px;
}

.sa-contact-item {
    margin: 1px 0;
    text-align: center;
}

.sa-contact-value {
    color: #666;
    direction: ltr;
}

/* ===== INVOICE INFORMATION SECTION ===== */

/* Invoice Type Section */
.sa-invoice-type-section {
    margin: 10px 0;
    text-align: center;
}

.sa-invoice-type {
    border: 1px solid #000;
    padding: 4px 8px;
    display: inline-block;
    background-color: #f0f0f0;
}

.sa-invoice-type-ar {
    font-size: 12px;
    font-weight: bold;
    color: #000;
    direction: rtl;
    display: block;
}

.sa-invoice-type-en {
    font-size: 10px;
    font-weight: bold;
    color: #333;
    direction: ltr;
    display: block;
}

/* Invoice Information Section */
.sa-invoice-info-section {
    margin: 8px 0;
    font-size: 10px;
}

.sa-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2px 0;
    padding: 1px 0;
}

.sa-info-labels {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.sa-info-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #000;
    direction: ltr;
    text-align: right;
}

/* Customer Section */
.sa-customer-section {
    margin: 8px 0;
    font-size: 10px;
}

.sa-section-separator {
    font-family: monospace;
    font-size: 8px;
    text-align: center;
    margin: 4px 0;
    color: #666;
    letter-spacing: 1px;
}

/* ===== PRODUCT TABLE SECTION ===== */

.sa-product-table-section {
    margin: 10px 0;
}

/* Table Header */
.sa-table-header {
    margin: 6px 0;
}

.sa-table-column-headers {
    display: grid;
    grid-template-columns: 40% 20% 15% 25%;
    gap: 2px;
    font-size: 9px;
    margin: 3px 0;
    padding: 2px 0;
}

.sa-col-product-name, .sa-col-unit-price, .sa-col-quantity, .sa-col-total {
    text-align: center;
}

.sa-header-ar {
    font-weight: bold;
    color: #000;
    direction: rtl;
    display: block;
    font-size: 9px;
}

.sa-header-en {
    font-weight: normal;
    color: #666;
    direction: ltr;
    display: block;
    font-size: 8px;
}

/* Product Rows */
.sa-product-rows {
    margin: 4px 0;
}

.sa-product-row {
    display: grid;
    grid-template-columns: 40% 20% 15% 25%;
    gap: 2px;
    margin: 3px 0;
    padding: 2px 0;
    font-size: 10px;
    align-items: start;
}

/* Product Name Column */
.sa-col-product-name {
    text-align: left;
    padding-right: 2px;
}

.sa-product-name-ar {
    font-weight: bold;
    color: #000;
    direction: rtl;
    text-align: right;
    font-size: 10px;
}

.sa-product-name-en {
    color: #666;
    direction: ltr;
    text-align: left;
    font-size: 9px;
}

/* Customer Note */
.sa-customer-note {
    margin: 2px 0;
    font-size: 8px;
    color: #666;
    font-style: italic;
}

.sa-note-icon {
    font-size: 8px;
    margin-right: 2px;
}

/* Lot Numbers */
.sa-lot-numbers {
    margin: 2px 0;
    font-size: 8px;
}

.sa-lot-item {
    margin: 1px 0;
    color: #666;
}

.sa-lot-label-ar, .sa-lot-label-en {
    font-size: 8px;
}

.sa-lot-value {
    font-family: monospace;
    font-weight: bold;
}

/* Unit Price, Quantity, Total Columns */
.sa-col-unit-price, .sa-col-quantity, .sa-col-total {
    text-align: right;
    font-family: 'Courier New', monospace;
}

.sa-currency {
    font-size: 8px;
    color: #666;
    margin-left: 2px;
}

.sa-amount {
    font-weight: bold;
    color: #000;
}

.sa-qty-value {
    font-weight: bold;
    color: #000;
}

.sa-qty-unit {
    font-size: 8px;
    color: #666;
    margin-left: 2px;
}

/* Subtotal Section */
.sa-subtotal-section {
    margin: 6px 0;
}

.sa-subtotal-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    font-weight: bold;
    padding: 2px 0;
}

.sa-subtotal-label {
    display: flex;
    flex-direction: column;
}

.sa-subtotal-amount {
    font-family: 'Courier New', monospace;
    color: #000;
}

/* ===== TOTALS SECTION ===== */

.sa-totals-section {
    margin: 10px 0;
}

.sa-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 3px 0;
    padding: 2px 0;
    font-size: 11px;
}

.sa-total-labels {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.sa-total-amount {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #000;
    text-align: right;
}

/* Discount Row */
.sa-discount {
    color: #d32f2f;
}

/* Before VAT Row */
.sa-before-vat {
    font-size: 10px;
}

/* VAT Row */
.sa-vat {
    font-size: 10px;
    color: #333;
}

/* Grand Total Row */
.sa-grand-total {
    margin: 6px 0;
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    background-color: #f5f5f5;
}

.sa-grand-total-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 14px;
    font-weight: bold;
}

.sa-grand-total-amount {
    font-size: 16px !important;
}

/* Total Quantity Row */
.sa-total-qty {
    font-size: 10px;
    color: #666;
}

.sa-qty-total {
    font-weight: bold;
    color: #000;
}

.sa-qty-label-ar, .sa-qty-label-en {
    font-size: 9px;
    margin-left: 2px;
}

/* ===== PAYMENT SECTION ===== */

.sa-payment-section {
    margin: 8px 0;
}

.sa-payment-header {
    text-align: center;
    margin: 4px 0;
    font-size: 11px;
    font-weight: bold;
}

.sa-payment-lines {
    margin: 4px 0;
}

.sa-payment-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2px 0;
    padding: 1px 0;
    font-size: 10px;
}

.sa-payment-method {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.sa-method-ar {
    font-weight: bold;
    color: #000;
    direction: rtl;
}

.sa-method-en {
    color: #666;
    direction: ltr;
    font-size: 9px;
}

.sa-method-name {
    font-weight: bold;
    color: #000;
}

.sa-payment-amount {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #000;
    text-align: right;
}

/* Change Section */
.sa-change-section {
    margin: 6px 0;
    border-top: 1px dashed #000;
    padding-top: 4px;
}

.sa-change-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    font-weight: bold;
}

.sa-change-labels {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.sa-change-amount {
    font-family: 'Courier New', monospace;
    color: #000;
    text-align: right;
}

/* ===== FOOTER SECTION ===== */

.sa-footer-section {
    margin: 10px 0;
}

/* QR Code Section */
.sa-qr-section {
    margin: 8px 0;
    text-align: center;
}

.sa-qr-label {
    margin: 4px 0;
    font-size: 9px;
}

.sa-qr-label .sa-label-ar {
    display: block;
    font-weight: bold;
    color: #000;
    direction: rtl;
    margin-bottom: 1px;
}

.sa-qr-label .sa-label-en {
    display: block;
    color: #666;
    direction: ltr;
}

.sa-qr-container {
    margin: 6px 0;
}

.sa-qr-code {
    width: 100px;
    height: 100px;
    display: block;
    margin: 0 auto;
    border: 1px solid #ddd;
}

/* 80mm printer QR code sizing */
@media (min-width: 500px) {
    .sa-qr-code {
        width: 120px;
        height: 120px;
    }
}

/* Thank You Section */
.sa-thank-you-section {
    margin: 8px 0;
    text-align: center;
}

.sa-thank-you-message {
    margin: 4px 0;
}

.sa-thank-you-ar {
    font-size: 12px;
    font-weight: bold;
    color: #000;
    direction: rtl;
    display: block;
    margin-bottom: 2px;
}

.sa-thank-you-en {
    font-size: 10px;
    color: #666;
    direction: ltr;
    display: block;
}

/* Store Contact Section */
.sa-store-contact {
    margin: 6px 0;
    text-align: center;
    font-size: 9px;
}

.sa-contact-phone, .sa-contact-website {
    margin: 1px 0;
    color: #666;
}

/* Payment Terminal Section */
.sa-payment-terminal-section {
    margin: 6px 0;
}

.sa-payment-terminal-receipt {
    font-family: monospace;
    font-size: 8px;
    color: #666;
    white-space: pre-wrap;
    margin: 4px 0;
    padding: 4px;
    border: 1px dashed #ccc;
}

/* ===== THERMAL PRINTER OPTIMIZATIONS ===== */

/* Print-specific styles */
@media print {
    .sa-thermal-receipt {
        max-width: none;
        width: 100%;
        margin: 0;
        padding: 4px;
        font-size: 11px;
    }

    .sa-company-logo {
        width: 60px;
        max-width: 80px;
    }

    .sa-business-name-ar {
        font-size: 14px;
    }

    .sa-business-name-en {
        font-size: 12px;
    }

    .sa-invoice-type-ar {
        font-size: 11px;
    }

    .sa-grand-total-content {
        font-size: 12px;
    }

    .sa-grand-total-amount {
        font-size: 14px !important;
    }

    .sa-qr-code {
        width: 80px;
        height: 80px;
    }

    .sa-section-separator {
        font-size: 7px;
    }
}

/* 58mm thermal printer specific styles */
@media (max-width: 400px) {
    .sa-thermal-receipt {
        font-size: 11px;
        padding: 6px;
    }

    .sa-table-column-headers {
        grid-template-columns: 45% 18% 12% 25%;
        font-size: 8px;
    }

    .sa-product-row {
        grid-template-columns: 45% 18% 12% 25%;
        font-size: 9px;
    }

    .sa-header-ar, .sa-header-en {
        font-size: 8px;
    }

    .sa-product-name-ar {
        font-size: 9px;
    }

    .sa-product-name-en {
        font-size: 8px;
    }
}

/* ===== UTILITY CLASSES ===== */

/* Arabic text direction and font */
.sa-rtl {
    direction: rtl;
    text-align: right;
    font-family: 'Tahoma', 'Arial Unicode MS', sans-serif;
}

/* English text direction and font */
.sa-ltr {
    direction: ltr;
    text-align: left;
    font-family: 'Arial', 'Helvetica', sans-serif;
}

/* Monospace for numbers and codes */
.sa-mono {
    font-family: 'Courier New', 'Monaco', monospace;
    letter-spacing: 0.5px;
}

/* Currency formatting */
.sa-currency-sar::before {
    content: "ر.س ";
    font-size: 0.8em;
    color: #666;
}

/* Hide elements that don't fit Saudi format */
.sa-thermal-receipt .pos-receipt-right-align {
    float: none;
    display: inline;
}

.sa-thermal-receipt .pos-receipt-amount {
    padding-left: 0;
    font-size: inherit;
}

/* Ensure proper line spacing */
.sa-thermal-receipt * {
    line-height: 1.2;
}

/* Override any conflicting styles */
.sa-thermal-receipt .pos-receipt-logo {
    width: auto;
    max-width: 120px;
}

.sa-thermal-receipt .pos-receipt-contact {
    font-size: inherit;
}

/* ===== SAUDI HEADER SPECIFIC STYLES ===== */

/* Business Information Section */
.sa-business-info-section {
    margin: 8px 0;
    text-align: center;
}

/* Cashier Section */
.sa-cashier-section {
    margin: 8px 0;
    text-align: center;
    font-size: 10px;
}

.sa-separator {
    color: #666;
    margin: 4px 0;
    font-family: monospace;
}

.sa-cashier-info {
    margin: 2px 0;
}

/* Tracking Number Sections */
.sa-tracking-section {
    margin: 6px 0;
    text-align: center;
    font-size: 10px;
}

.sa-tracking-number {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2px 0;
}

.sa-large-tracking-section {
    margin: 12px 0;
    text-align: center;
}

.sa-large-tracking-number {
    font-size: 24px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    color: #000;
    letter-spacing: 2px;
}

/* Custom Header Section */
.sa-custom-header {
    margin: 8px 0;
    font-size: 10px;
    color: #333;
}

/* Header Separator */
.sa-header-separator {
    height: 12px;
    margin: 8px 0;
}

/* ===== SAUDI ORDER RECEIPT STYLES ===== */

/* Products Table Header */
.sa-products-header {
    margin: 8px 0;
    font-size: 10px;
}

.sa-separator-line {
    text-align: center;
    color: #666;
    font-family: monospace;
    margin: 2px 0;
}

.sa-table-header {
    margin: 4px 0;
}

.sa-header-row {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    color: #000;
}

.sa-col-item {
    flex: 2;
    text-align: left;
}

.sa-col-qty {
    flex: 1;
    text-align: center;
}

.sa-col-price {
    flex: 1;
    text-align: center;
}

.sa-col-total {
    flex: 1;
    text-align: right;
}

/* Order Widget Styling */
.sa-order-widget {
    margin: 4px 0;
}

/* Total Section */
.sa-total-section {
    margin: 8px 0;
    font-size: 11px;
}

.sa-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2px 0;
    padding: 1px 0;
}

.sa-total-label-ar {
    font-weight: bold;
    color: #000;
    direction: rtl;
    font-size: 10px;
    flex: 1;
}

.sa-total-label-en {
    font-weight: normal;
    color: #666;
    direction: ltr;
    font-size: 9px;
    flex: 1;
}

.sa-total-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #000;
    direction: ltr;
    text-align: right;
    min-width: 80px;
}

/* Grand Total */
.sa-grand-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px 0;
    padding: 4px 0;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    font-weight: bold;
}

.sa-grand-total-label-ar {
    font-size: 12px;
    font-weight: bold;
    color: #000;
    direction: rtl;
}

.sa-grand-total-label-en {
    font-size: 11px;
    font-weight: bold;
    color: #333;
    direction: ltr;
}

.sa-grand-total-value {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    color: #000;
    direction: ltr;
}

/* Footer Section */
.sa-footer-section {
    margin: 12px 0;
    text-align: center;
}

.sa-thank-you {
    margin: 8px 0;
}

.sa-thank-you-ar {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    direction: rtl;
    margin: 2px 0;
}

.sa-thank-you-en {
    font-size: 12px;
    font-weight: bold;
    color: #333;
    direction: ltr;
    margin: 2px 0;
}

/* QR Code Section */
.sa-qr-section {
    margin: 10px 0;
    text-align: center;
}

.sa-qr-instructions {
    font-size: 9px;
    margin: 4px 0;
}

/* Store Information */
.sa-store-info {
    margin: 8px 0;
    font-size: 10px;
}

.sa-store-slogan-ar {
    direction: rtl;
    color: #666;
    margin: 2px 0;
}

.sa-store-slogan-en {
    direction: ltr;
    color: #666;
    margin: 2px 0;
}

/* 80mm printer adjustments for header */
@media (min-width: 500px) {
    .sa-large-tracking-number {
        font-size: 32px;
        letter-spacing: 3px;
    }

    .sa-business-info-section {
        margin: 12px 0;
    }

    .sa-cashier-section {
        font-size: 11px;
    }

    .sa-tracking-section {
        font-size: 11px;
    }
}

/* ===== FINAL OPTIMIZATIONS AND OVERRIDES ===== */

/* Ensure Saudi receipt takes precedence over default styles */
.pos-receipt.sa-thermal-receipt {
    font-family: 'Tahoma', 'Arial Unicode MS', 'DejaVu Sans', sans-serif !important;
    line-height: 1.2 !important;
    color: #000 !important;
    background: #fff !important;
}

/* Remove default Odoo receipt padding and margins that interfere */
.pos-receipt.sa-thermal-receipt .pos-receipt-amount,
.pos-receipt.sa-thermal-receipt .pos-receipt-contact,
.pos-receipt.sa-thermal-receipt .pos-receipt-order-data {
    padding: 0 !important;
    margin: 0 !important;
    font-size: inherit !important;
}

/* Ensure proper text alignment for Arabic content */
.sa-thermal-receipt .sa-label-ar,
.sa-thermal-receipt .sa-header-ar,
.sa-thermal-receipt .sa-product-name-ar,
.sa-thermal-receipt .sa-thank-you-ar {
    direction: rtl !important;
    font-family: 'Tahoma', 'Arial Unicode MS', sans-serif !important;
}

/* Ensure proper text alignment for English content */
.sa-thermal-receipt .sa-label-en,
.sa-thermal-receipt .sa-header-en,
.sa-thermal-receipt .sa-product-name-en,
.sa-thermal-receipt .sa-thank-you-en {
    direction: ltr !important;
    font-family: 'Arial', 'Helvetica', sans-serif !important;
}

/* Ensure monospace fonts for numbers and amounts */
.sa-thermal-receipt .sa-value,
.sa-thermal-receipt .sa-amount,
.sa-thermal-receipt .sa-info-value,
.sa-thermal-receipt .sa-qty-value {
    font-family: 'Courier New', 'Monaco', monospace !important;
    direction: ltr !important;
}

/* Remove any default borders or backgrounds that interfere */
.sa-thermal-receipt .pos-receipt-taxes,
.sa-thermal-receipt .paymentlines {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Ensure QR code is properly displayed */
.sa-thermal-receipt .sa-qr-code {
    display: block !important;
    margin: 6px auto !important;
}

/* Final responsive adjustments */
@media (max-width: 350px) {
    .sa-thermal-receipt {
        font-size: 10px;
        padding: 4px;
    }

    .sa-business-name-ar {
        font-size: 14px;
    }

    .sa-business-name-en {
        font-size: 12px;
    }

    .sa-table-column-headers,
    .sa-product-row {
        grid-template-columns: 50% 16% 10% 24%;
        font-size: 8px;
    }

    .sa-qr-code {
        width: 80px;
        height: 80px;
    }
}

/* Ensure proper printing */
@page {
    margin: 0;
    size: 58mm auto;
}

@media print {
    body {
        margin: 0;
        padding: 0;
    }

    .sa-thermal-receipt {
        margin: 0 !important;
        padding: 2mm !important;
        box-shadow: none !important;
        border: none !important;
    }
}
