/** @odoo-module */
import { Order } from "@point_of_sale/app/store/models";
import { patch } from "@web/core/utils/patch";
import { Component } from "@odoo/owl";
import { ProductScreen } from
    "@point_of_sale/app/screens/product_screen/product_screen";
import { usePos } from "@point_of_sale/app/store/pos_hook";
export class PosDiscountButton extends Component {
    static template = "PosDiscountButton";
    setup() {
        this.pos = usePos();
    }
    async onClick() {
        const order = this.pos.get_order();
        if (order.selected_orderline) {
            order.selected_orderline.set_discount(5);
        }
    }
}
ProductScreen.addControlButton({
    component: PosDiscountButton,
    condition: function () {
        return true;
    }

});


 patch(Order.prototype, {
     saved_amount() {
         const order = this;
         return order.orderlines.reduce((rem, line) => {
             var diffrence = (line.product.lst_price * line.quantity) -
                 line.get_base_price();
             return rem + diffrence;
         }, 0);
     },
     export_for_printing() {
         const json = super.export_for_printing(...arguments);
         var savedAmount = this.saved_amount();
         if (savedAmount > 0) {
             json.saved_amount = this.env.utils.formatCurrency(savedAmount);
         }
         return json;
     }
 })



// ----------------------------------
///** @odoo-module */
//import { Order } from "@point_of_sale/app/store/models";
//import { patch } from "@web/core/utils/patch";
//patch(Order.prototype, {
//    saved_amount() {
//        const order = this;
//        return order.orderlines.reduce((rem, line) => {
//            var diffrence = (line.product.lst_price * line.quantity) - line.get_base_price();
//            return rem + diffrence;
//        }, 0);
//    },
//    export_for_printing() {
//        const json = super.export_for_printing(...arguments);
//        var savedAmount = this.saved_amount();
//        if (savedAmount > 0) {
//            json.saved_amount = this.env.utils.formatCurrency(savedAmount);
//        }
//        return json;
//    }
//})
