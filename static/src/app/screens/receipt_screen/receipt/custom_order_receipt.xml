<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Saudi Arabian Thermal Receipt Template - Inherits from point_of_sale.OrderReceipt -->
    <t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">

        <!-- Wrap the entire receipt in Saudi Arabian thermal receipt container -->
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="class">pos-receipt sa-thermal-receipt</attribute>
        </xpath>

        <!-- Add Invoice Information Section after header -->
        <xpath expr="//ReceiptHeader" position="after">
            <!-- Invoice Type Section -->
            <div class="sa-invoice-type-section">
                <div class="sa-invoice-type">
                    <span class="sa-invoice-type-ar">فاتورة ضريبية مبسطة</span>
                    <span class="sa-invoice-type-en">Simplified Tax Invoice</span>
                </div>
            </div>

            <!-- Invoice Information Section -->
            <div class="sa-invoice-info-section">
                <!-- Invoice Number -->
                <div class="sa-info-row sa-invoice-number">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">رقم الفاتورة:</span>
                        <span class="sa-label-en">Invoice No:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.name"/>
                </div>

                <!-- Date and Time -->
                <div class="sa-info-row sa-datetime">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">التاريخ والوقت:</span>
                        <span class="sa-label-en">Date &amp; Time:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.date"/>
                </div>

                <!-- Cashier Information -->
                <div class="sa-info-row sa-cashier" t-if="props.data.cashier">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">أمين الصندوق:</span>
                        <span class="sa-label-en">Cashier:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.cashier"/>
                </div>
            </div>

            <!-- Customer Information Section (conditional) -->
            <div class="sa-customer-section" t-if="props.data.client">
                <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>

                <!-- Customer Name -->
                <div class="sa-info-row sa-customer-name">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">اسم العميل:</span>
                        <span class="sa-label-en">Customer:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.client"/>
                </div>

                <!-- Customer VAT (if available) -->
                <div class="sa-info-row sa-customer-vat" t-if="props.data.client_vat">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">الرقم الضريبي للعميل:</span>
                        <span class="sa-label-en">Customer VAT:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.client_vat"/>
                </div>
            </div>
        </xpath>

        <!-- Replace order lines with Saudi Arabian product table format -->
        <xpath expr="//OrderWidget" position="replace">
            <!-- Product Table Section -->
            <div class="sa-product-table-section">
                <!-- Table Header -->
                <div class="sa-table-header">
                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>

                    <!-- Column Headers -->
                    <div class="sa-table-column-headers">
                        <div class="sa-col-product-name">
                            <span class="sa-header-ar">اسم المنتج</span>
                            <span class="sa-header-en">Product Name</span>
                        </div>
                        <div class="sa-col-unit-price">
                            <span class="sa-header-ar">سعر الوحدة</span>
                            <span class="sa-header-en">Unit Price</span>
                        </div>
                        <div class="sa-col-quantity">
                            <span class="sa-header-ar">الكمية</span>
                            <span class="sa-header-en">Qty</span>
                        </div>
                        <div class="sa-col-total">
                            <span class="sa-header-ar">المجموع</span>
                            <span class="sa-header-en">Total</span>
                        </div>
                    </div>

                    <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>
                </div>

                <!-- Product Rows -->
                <div class="sa-product-rows">
                    <t t-foreach="props.data.orderlines" t-as="line" t-key="line.id">
                        <div class="sa-product-row">
                            <!-- Product Name Column -->
                            <div class="sa-col-product-name">
                                <div class="sa-product-name-ar" t-esc="line.product_name_ar or line.product_name"/>
                                <div class="sa-product-name-en" t-if="line.product_name_ar" t-esc="line.product_name"/>

                                <!-- Customer Note -->
                                <div class="sa-customer-note" t-if="line.customerNote">
                                    <span class="sa-note-icon">📝</span>
                                    <span t-esc="line.customerNote"/>
                                </div>

                                <!-- Lot/Serial Numbers -->
                                <div class="sa-lot-numbers" t-if="line.pack_lot_lines">
                                    <t t-foreach="line.pack_lot_lines" t-as="lot" t-key="lot.cid">
                                        <div class="sa-lot-item">
                                            <t t-if="lot.order_line.product.tracking == 'lot'">
                                                <span class="sa-lot-label-ar">رقم اللوط:</span>
                                                <span class="sa-lot-label-en">Lot:</span>
                                            </t>
                                            <t t-else="">
                                                <span class="sa-lot-label-ar">الرقم التسلسلي:</span>
                                                <span class="sa-lot-label-en">SN:</span>
                                            </t>
                                            <span class="sa-lot-value" t-esc="lot.lot_name"/>
                                        </div>
                                    </t>
                                </div>
                            </div>

                            <!-- Unit Price Column -->
                            <div class="sa-col-unit-price">
                                <span class="sa-currency">ر.س</span>
                                <span class="sa-amount" t-esc="props.formatCurrency(line.price_unit, false)"/>
                            </div>

                            <!-- Quantity Column -->
                            <div class="sa-col-quantity">
                                <span class="sa-qty-value" t-esc="line.qty"/>
                                <span class="sa-qty-unit" t-if="line.product_uom">
                                    <t t-esc="line.product_uom"/>
                                </span>
                            </div>

                            <!-- Total Column -->
                            <div class="sa-col-total">
                                <span class="sa-currency">ر.س</span>
                                <span class="sa-amount" t-esc="props.formatCurrency(line.price_subtotal_incl, false)"/>
                            </div>
                        </div>
                    </t>
                </div>

                <!-- Subtotal Line -->
                <div class="sa-subtotal-section">
                    <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>
                    <div class="sa-subtotal-row">
                        <div class="sa-subtotal-label">
                            <span class="sa-label-ar">المجموع الفرعي:</span>
                            <span class="sa-label-en">Subtotal:</span>
                        </div>
                        <div class="sa-subtotal-amount">
                            <span class="sa-currency">ر.س</span>
                            <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_without_tax, false)"/>
                        </div>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- Replace totals section with Saudi Arabian format -->
        <xpath expr="//div[@class='pos-receipt-amount'][1]" position="replace">
            <!-- Saudi Arabian Totals Section -->
            <div class="sa-totals-section">
                <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>

                <!-- Total Discount (if applicable) -->
                <div class="sa-total-row sa-discount" t-if="props.data.total_discount and props.data.total_discount > 0">
                    <div class="sa-total-labels">
                        <span class="sa-label-ar">إجمالي الخصم:</span>
                        <span class="sa-label-en">Total Discount:</span>
                    </div>
                    <div class="sa-total-amount">
                        <span class="sa-currency">ر.س</span>
                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_discount, false)"/>
                    </div>
                </div>

                <!-- Total Before VAT -->
                <div class="sa-total-row sa-before-vat">
                    <div class="sa-total-labels">
                        <span class="sa-label-ar">المجموع قبل الضريبة:</span>
                        <span class="sa-label-en">Total before VAT:</span>
                    </div>
                    <div class="sa-total-amount">
                        <span class="sa-currency">ر.س</span>
                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_without_tax, false)"/>
                    </div>
                </div>

                <!-- VAT 15% -->
                <div class="sa-total-row sa-vat" t-if="props.data.amount_tax > 0">
                    <div class="sa-total-labels">
                        <span class="sa-label-ar">ضريبة القيمة المضافة 15%:</span>
                        <span class="sa-label-en">VAT 15%:</span>
                    </div>
                    <div class="sa-total-amount">
                        <span class="sa-currency">ر.س</span>
                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.amount_tax, false)"/>
                    </div>
                </div>

                <!-- Grand Total -->
                <div class="sa-total-row sa-grand-total">
                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>
                    <div class="sa-grand-total-content">
                        <div class="sa-total-labels">
                            <span class="sa-label-ar">المجموع الإجمالي:</span>
                            <span class="sa-label-en">Grand Total:</span>
                        </div>
                        <div class="sa-total-amount sa-grand-total-amount">
                            <span class="sa-currency">ر.س</span>
                            <span class="sa-amount" t-esc="props.formatCurrency(props.data.amount_total, false)"/>
                        </div>
                    </div>
                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>
                </div>

                <!-- Total Quantity -->
                <div class="sa-total-row sa-total-qty">
                    <div class="sa-total-labels">
                        <span class="sa-label-ar">إجمالي الكمية:</span>
                        <span class="sa-label-en">Total Quantity:</span>
                    </div>
                    <div class="sa-total-amount">
                        <span class="sa-qty-total" t-esc="props.data.orderlines.reduce((sum, line) => sum + line.qty, 0)"/>
                        <span class="sa-qty-label-ar">قطعة</span>
                        <span class="sa-qty-label-en">items</span>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- Replace payment section with Saudi Arabian format -->
        <xpath expr="//div[@class='paymentlines']" position="replace">
            <!-- Saudi Arabian Payment Section -->
            <div class="sa-payment-section">
                <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>

                <!-- Payment Methods Header -->
                <div class="sa-payment-header">
                    <span class="sa-label-ar">طرق الدفع:</span>
                    <span class="sa-label-en">Payment Methods:</span>
                </div>

                <!-- Payment Lines -->
                <div class="sa-payment-lines">
                    <t t-foreach="props.data.paymentlines" t-as="line" t-key="line_index">
                        <div class="sa-payment-row">
                            <div class="sa-payment-method">
                                <!-- Cash Payment -->
                                <t t-if="line.name.toLowerCase().includes('cash') or line.name.toLowerCase().includes('نقد')">
                                    <span class="sa-method-ar">نقداً:</span>
                                    <span class="sa-method-en">Cash:</span>
                                </t>
                                <!-- Card Payment -->
                                <t t-elif="line.name.toLowerCase().includes('card') or line.name.toLowerCase().includes('بطاقة')">
                                    <span class="sa-method-ar">بطاقة:</span>
                                    <span class="sa-method-en">Card:</span>
                                </t>
                                <!-- Other Payment Methods -->
                                <t t-else="">
                                    <span class="sa-method-name" t-esc="line.name"/>:
                                </t>
                            </div>
                            <div class="sa-payment-amount">
                                <span class="sa-currency">ر.س</span>
                                <span class="sa-amount" t-esc="props.formatCurrency(line.amount, false)"/>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        </xpath>

        <!-- Replace change section with Saudi Arabian format -->
        <xpath expr="//div[@class='pos-receipt-amount receipt-change mt-2']" position="replace">
            <div class="sa-change-section" t-if="props.data.change > 0">
                <div class="sa-change-row">
                    <div class="sa-change-labels">
                        <span class="sa-label-ar">الباقي:</span>
                        <span class="sa-label-en">Change:</span>
                    </div>
                    <div class="sa-change-amount">
                        <span class="sa-currency">ر.س</span>
                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.change, false)"/>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- Replace footer with Saudi Arabian format -->
        <xpath expr="//div[@class='pos-receipt-order-data'][last()]" position="before">
            <!-- Saudi Arabian Footer Section -->
            <div class="sa-footer-section">
                <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>

                <!-- QR Code Section -->
                <div class="sa-qr-section" t-if="props.data.pos_qr_code">
                    <div class="sa-qr-label">
                        <span class="sa-label-ar">امسح الرمز للحصول على الفاتورة الإلكترونية</span>
                        <span class="sa-label-en">Scan for E-Invoice</span>
                    </div>
                    <div class="sa-qr-container">
                        <img id="posqrcode" t-att-src="props.data.pos_qr_code" class="sa-qr-code"/>
                    </div>
                </div>

                <!-- Thank You Message -->
                <div class="sa-thank-you-section">
                    <div class="sa-thank-you-message">
                        <span class="sa-thank-you-ar">شكراً لزيارتكم</span>
                        <span class="sa-thank-you-en">Thank you for your visit</span>
                    </div>
                </div>

                <!-- Store Contact Information -->
                <div class="sa-store-contact" t-if="props.data.company.phone or props.data.company.website">
                    <div class="sa-contact-phone" t-if="props.data.company.phone">
                        <span class="sa-contact-value" t-esc="props.data.company.phone"/>
                    </div>
                    <div class="sa-contact-website" t-if="props.data.company.website">
                        <span class="sa-contact-value" t-esc="props.data.company.website"/>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- Remove the original "Powered by Odoo" section -->
        <xpath expr="//div[@class='pos-receipt-order-data'][last()]" position="replace">
            <!-- This section is removed to maintain clean Saudi Arabian format -->
        </xpath>

        <!-- Remove other default sections that don't fit Saudi format -->
        <xpath expr="//div[@class='before-footer']" position="replace">
            <!-- Removed for clean Saudi format -->
        </xpath>

        <xpath expr="//div[@class='after-footer']" position="replace">
            <!-- Payment terminal receipts can be added here if needed -->
            <div class="sa-payment-terminal-section">
                <t t-foreach="props.data.paymentlines" t-as="line" t-key="line_index">
                    <t t-if="line.ticket">
                        <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>
                        <div class="sa-payment-terminal-receipt">
                            <pre t-esc="line.ticket" />
                        </div>
                    </t>
                </t>
            </div>
        </xpath>

        <!-- Remove shipping date section -->
        <xpath expr="//t[@t-if='props.data.shippingDate']" position="replace">
            <!-- Removed for Saudi format -->
        </xpath>

    </t>
</templates>
