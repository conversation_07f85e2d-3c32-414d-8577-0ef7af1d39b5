<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Saudi Arabian Thermal Receipt Header Template - Inherits from point_of_sale.ReceiptHeader -->
    <t t-inherit="point_of_sale.ReceiptHeader" t-inherit-mode="extension">

        <!-- Replace entire header with Saudi Arabian format -->
        <xpath expr="//img[@class='pos-receipt-logo']" position="replace">
            <!-- Company Logo Section -->
            <div class="sa-logo-section">
                <img t-attf-src="/web/image?model=res.company&amp;id={{props.data.company.id}}&amp;field=logo"
                     alt="Company Logo"
                     class="sa-company-logo"/>
            </div>
        </xpath>

        <!-- Replace contact section with Saudi format -->
        <xpath expr="//div[@class='pos-receipt-contact']" position="replace">
            <!-- Business Name Section (Arabic/English) -->
            <div class="sa-business-name-section">
                <div class="sa-business-name-ar" t-if="props.data.company.partner_id?.[1]">
                    <t t-esc="props.data.company.partner_id[1]" />
                </div>
                <div class="sa-business-name-en" t-if="props.data.company.partner_id?.[1]">
                    <t t-esc="props.data.company.partner_id[1]" />
                </div>
            </div>

            <!-- Business Address Section -->
            <div class="sa-address-section">
                <!-- Arabic Address (Primary) -->
                <div class="sa-address-ar" t-if="props.data.company.street">
                    <t t-esc="props.data.company.street" />
                    <t t-if="props.data.company.street2">, <t t-esc="props.data.company.street2" /></t>
                </div>
                <div class="sa-address-ar" t-if="props.data.company.city">
                    <t t-esc="props.data.company.city" />
                    <t t-if="props.data.company.state_id">, <t t-esc="props.data.company.state_id[1]" /></t>
                    <t t-if="props.data.company.zip"> <t t-esc="props.data.company.zip" /></t>
                </div>

                <!-- English Address (Secondary) -->
                <div class="sa-address-en" t-if="props.data.company.street">
                    <t t-esc="props.data.company.street" />
                    <t t-if="props.data.company.street2">, <t t-esc="props.data.company.street2" /></t>
                </div>
                <div class="sa-address-en" t-if="props.data.company.city">
                    <t t-esc="props.data.company.city" />
                    <t t-if="props.data.company.state_id">, <t t-esc="props.data.company.state_id[1]" /></t>
                    <t t-if="props.data.company.zip"> <t t-esc="props.data.company.zip" /></t>
                </div>
            </div>

            <!-- Commercial Registration Section -->
            <div class="sa-registration-section">
                <div class="sa-cr-number">
                    <span class="sa-label-ar">السجل التجاري:</span>
                    <span class="sa-label-en">CR No:</span>
                    <span class="sa-value">1009120325</span>
                </div>

                <!-- VAT Registration Section -->
                <div class="sa-vat-number" t-if="props.data.company.vat">
                    <span class="sa-label-ar">الرقم الضريبي:</span>
                    <span class="sa-label-en">VAT No:</span>
                    <span class="sa-value" t-esc="props.data.company.vat"/>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="sa-contact-section">
                <div class="sa-contact-item" t-if="props.data.company.phone">
                    <span class="sa-contact-value" t-esc="props.data.company.phone"/>
                </div>
                <div class="sa-contact-item" t-if="props.data.company.website">
                    <span class="sa-contact-value" t-esc="props.data.company.website"/>
                </div>
            </div>
        </xpath>

        <!-- Remove the big tracking number display from header -->
        <xpath expr="//h1[@class='tracking-number text-center']" position="replace">
            <!-- This will be moved to invoice information section -->
        </xpath>

        <!-- Remove the small tracking number from header -->
        <xpath expr="//div[@class='fw-bolder']" position="replace">
            <!-- This will be moved to invoice information section -->
        </xpath>

        <!-- Remove cashier info from header -->
        <xpath expr="//div[@class='cashier']" position="replace">
            <!-- This will be moved to invoice information section -->
        </xpath>

    </t>
</templates>
