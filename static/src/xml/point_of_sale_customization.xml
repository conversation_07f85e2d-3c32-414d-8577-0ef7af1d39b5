<?xml version="1.0" encoding="UTF-8"?>
<!--<templates id="template" xml:space="preserve">-->
<!--    &lt;!&ndash; Saudi Arabian Thermal Receipt Template - Inherits from point_of_sale.OrderReceipt &ndash;&gt;-->
<!--    <t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">-->

<!--        &lt;!&ndash; Wrap the entire receipt in Saudi Arabian thermal receipt container &ndash;&gt;-->
<!--        <xpath expr="//div[@class='pos-receipt']" position="attributes">-->
<!--            <attribute name="class">pos-receipt sa-thermal-receipt</attribute>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Add Invoice Information Section after header &ndash;&gt;-->
<!--        <xpath expr="//ReceiptHeader" position="after">-->
<!--            &lt;!&ndash; Invoice Type Section &ndash;&gt;-->
<!--            <div class="sa-invoice-type-section">-->
<!--                <div class="sa-invoice-type">-->
<!--                    <span class="sa-invoice-type-ar">فاتورة ضريبية مبسطة</span>-->
<!--                    <span class="sa-invoice-type-en">Simplified Tax Invoice</span>-->
<!--                </div>-->
<!--            </div>-->

<!--            &lt;!&ndash; Invoice Information Section &ndash;&gt;-->
<!--            <div class="sa-invoice-info-section">-->
<!--                &lt;!&ndash; Invoice Number &ndash;&gt;-->
<!--                <div class="sa-info-row sa-invoice-number">-->
<!--                    <div class="sa-info-labels">-->
<!--                        <span class="sa-label-ar">رقم الفاتورة:</span>-->
<!--                        <span class="sa-label-en">Invoice No:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-info-value" t-esc="props.data.name"/>-->
<!--                </div>-->

<!--                &lt;!&ndash; Date and Time &ndash;&gt;-->
<!--                <div class="sa-info-row sa-datetime">-->
<!--                    <div class="sa-info-labels">-->
<!--                        <span class="sa-label-ar">التاريخ والوقت:</span>-->
<!--                        <span class="sa-label-en">Date &amp; Time:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-info-value" t-esc="props.data.date"/>-->
<!--                </div>-->

<!--                &lt;!&ndash; Cashier Information &ndash;&gt;-->
<!--                <div class="sa-info-row sa-cashier" t-if="props.data.cashier">-->
<!--                    <div class="sa-info-labels">-->
<!--                        <span class="sa-label-ar">أمين الصندوق:</span>-->
<!--                        <span class="sa-label-en">Cashier:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-info-value" t-esc="props.data.cashier"/>-->
<!--                </div>-->
<!--            </div>-->

<!--            &lt;!&ndash; Customer Information Section (conditional) &ndash;&gt;-->
<!--            <div class="sa-customer-section" t-if="props.data.client">-->
<!--                <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>-->

<!--                &lt;!&ndash; Customer Name &ndash;&gt;-->
<!--                <div class="sa-info-row sa-customer-name">-->
<!--                    <div class="sa-info-labels">-->
<!--                        <span class="sa-label-ar">اسم العميل:</span>-->
<!--                        <span class="sa-label-en">Customer:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-info-value" t-esc="props.data.client"/>-->
<!--                </div>-->

<!--                &lt;!&ndash; Customer VAT (if available) &ndash;&gt;-->
<!--                <div class="sa-info-row sa-customer-vat" t-if="props.data.client_vat">-->
<!--                    <div class="sa-info-labels">-->
<!--                        <span class="sa-label-ar">الرقم الضريبي للعميل:</span>-->
<!--                        <span class="sa-label-en">Customer VAT:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-info-value" t-esc="props.data.client_vat"/>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Replace order lines with Saudi Arabian product table format &ndash;&gt;-->
<!--        <xpath expr="//OrderWidget" position="replace">-->
<!--            &lt;!&ndash; Product Table Section &ndash;&gt;-->
<!--            <div class="sa-product-table-section">-->
<!--                &lt;!&ndash; Table Header &ndash;&gt;-->
<!--                <div class="sa-table-header">-->
<!--                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>-->

<!--                    &lt;!&ndash; Column Headers &ndash;&gt;-->
<!--                    <div class="sa-table-column-headers">-->
<!--                        <div class="sa-col-product-name">-->
<!--                            <span class="sa-header-ar">اسم المنتج</span>-->
<!--                            <span class="sa-header-en">Product Name</span>-->
<!--                        </div>-->
<!--                        <div class="sa-col-unit-price">-->
<!--                            <span class="sa-header-ar">سعر الوحدة</span>-->
<!--                            <span class="sa-header-en">Unit Price</span>-->
<!--                        </div>-->
<!--                        <div class="sa-col-quantity">-->
<!--                            <span class="sa-header-ar">الكمية</span>-->
<!--                            <span class="sa-header-en">Qty</span>-->
<!--                        </div>-->
<!--                        <div class="sa-col-total">-->
<!--                            <span class="sa-header-ar">المجموع</span>-->
<!--                            <span class="sa-header-en">Total</span>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Product Rows &ndash;&gt;-->
<!--                <div class="sa-product-rows">-->
<!--                    <t t-foreach="props.data.orderlines" t-as="line" t-key="line.id">-->
<!--                        <div class="sa-product-row">-->
<!--                            &lt;!&ndash; Product Name Column &ndash;&gt;-->
<!--                            <div class="sa-col-product-name">-->
<!--                                <div class="sa-product-name-ar" t-esc="line.product_name_ar or line.product_name"/>-->
<!--                                <div class="sa-product-name-en" t-if="line.product_name_ar" t-esc="line.product_name"/>-->

<!--                                &lt;!&ndash; Customer Note &ndash;&gt;-->
<!--                                <div class="sa-customer-note" t-if="line.customerNote">-->
<!--                                    <span class="sa-note-icon">📝</span>-->
<!--                                    <span t-esc="line.customerNote"/>-->
<!--                                </div>-->

<!--                                &lt;!&ndash; Lot/Serial Numbers &ndash;&gt;-->
<!--                                <div class="sa-lot-numbers" t-if="line.pack_lot_lines">-->
<!--                                    <t t-foreach="line.pack_lot_lines" t-as="lot" t-key="lot.cid">-->
<!--                                        <div class="sa-lot-item">-->
<!--                                            <t t-if="lot.order_line.product.tracking == 'lot'">-->
<!--                                                <span class="sa-lot-label-ar">رقم اللوط:</span>-->
<!--                                                <span class="sa-lot-label-en">Lot:</span>-->
<!--                                            </t>-->
<!--                                            <t t-else="">-->
<!--                                                <span class="sa-lot-label-ar">الرقم التسلسلي:</span>-->
<!--                                                <span class="sa-lot-label-en">SN:</span>-->
<!--                                            </t>-->
<!--                                            <span class="sa-lot-value" t-esc="lot.lot_name"/>-->
<!--                                        </div>-->
<!--                                    </t>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            &lt;!&ndash; Unit Price Column &ndash;&gt;-->
<!--                            <div class="sa-col-unit-price">-->
<!--                                <span class="sa-currency">ر.س</span>-->
<!--                                <span class="sa-amount" t-esc="props.formatCurrency(line.price_unit, false)"/>-->
<!--                            </div>-->

<!--                            &lt;!&ndash; Quantity Column &ndash;&gt;-->
<!--                            <div class="sa-col-quantity">-->
<!--                                <span class="sa-qty-value" t-esc="line.qty"/>-->
<!--                                <span class="sa-qty-unit" t-if="line.product_uom">-->
<!--                                    <t t-esc="line.product_uom"/>-->
<!--                                </span>-->
<!--                            </div>-->

<!--                            &lt;!&ndash; Total Column &ndash;&gt;-->
<!--                            <div class="sa-col-total">-->
<!--                                <span class="sa-currency">ر.س</span>-->
<!--                                <span class="sa-amount" t-esc="props.formatCurrency(line.price_subtotal_incl, false)"/>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </t>-->
<!--                </div>-->

<!--                &lt;!&ndash; Subtotal Line &ndash;&gt;-->
<!--                <div class="sa-subtotal-section">-->
<!--                    <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>-->
<!--                    <div class="sa-subtotal-row">-->
<!--                        <div class="sa-subtotal-label">-->
<!--                            <span class="sa-label-ar">المجموع الفرعي:</span>-->
<!--                            <span class="sa-label-en">Subtotal:</span>-->
<!--                        </div>-->
<!--                        <div class="sa-subtotal-amount">-->
<!--                            <span class="sa-currency">ر.س</span>-->
<!--                            <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_without_tax, false)"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Replace totals section with Saudi Arabian format &ndash;&gt;-->
<!--        <xpath expr="//div[@class='pos-receipt-amount'][1]" position="replace">-->
<!--            &lt;!&ndash; Saudi Arabian Totals Section &ndash;&gt;-->
<!--            <div class="sa-totals-section">-->
<!--                <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>-->

<!--                &lt;!&ndash; Total Discount (if applicable) &ndash;&gt;-->
<!--                <div class="sa-total-row sa-discount" t-if="props.data.total_discount and props.data.total_discount > 0">-->
<!--                    <div class="sa-total-labels">-->
<!--                        <span class="sa-label-ar">إجمالي الخصم:</span>-->
<!--                        <span class="sa-label-en">Total Discount:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-total-amount">-->
<!--                        <span class="sa-currency">ر.س</span>-->
<!--                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_discount, false)"/>-->
<!--                    </div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Total Before VAT &ndash;&gt;-->
<!--                <div class="sa-total-row sa-before-vat">-->
<!--                    <div class="sa-total-labels">-->
<!--                        <span class="sa-label-ar">المجموع قبل الضريبة:</span>-->
<!--                        <span class="sa-label-en">Total before VAT:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-total-amount">-->
<!--                        <span class="sa-currency">ر.س</span>-->
<!--                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.total_without_tax, false)"/>-->
<!--                    </div>-->
<!--                </div>-->

<!--                &lt;!&ndash; VAT 15% &ndash;&gt;-->
<!--                <div class="sa-total-row sa-vat" t-if="props.data.amount_tax > 0">-->
<!--                    <div class="sa-total-labels">-->
<!--                        <span class="sa-label-ar">ضريبة القيمة المضافة 15%:</span>-->
<!--                        <span class="sa-label-en">VAT 15%:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-total-amount">-->
<!--                        <span class="sa-currency">ر.س</span>-->
<!--                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.amount_tax, false)"/>-->
<!--                    </div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Grand Total &ndash;&gt;-->
<!--                <div class="sa-total-row sa-grand-total">-->
<!--                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>-->
<!--                    <div class="sa-grand-total-content">-->
<!--                        <div class="sa-total-labels">-->
<!--                            <span class="sa-label-ar">المجموع الإجمالي:</span>-->
<!--                            <span class="sa-label-en">Grand Total:</span>-->
<!--                        </div>-->
<!--                        <div class="sa-total-amount sa-grand-total-amount">-->
<!--                            <span class="sa-currency">ر.س</span>-->
<!--                            <span class="sa-amount" t-esc="props.formatCurrency(props.data.amount_total, false)"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Total Quantity &ndash;&gt;-->
<!--                <div class="sa-total-row sa-total-qty">-->
<!--                    <div class="sa-total-labels">-->
<!--                        <span class="sa-label-ar">إجمالي الكمية:</span>-->
<!--                        <span class="sa-label-en">Total Quantity:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-total-amount">-->
<!--                        <span class="sa-qty-total" t-esc="props.data.orderlines.reduce((sum, line) => sum + line.qty, 0)"/>-->
<!--                        <span class="sa-qty-label-ar">قطعة</span>-->
<!--                        <span class="sa-qty-label-en">items</span>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Replace payment section with Saudi Arabian format &ndash;&gt;-->
<!--        <xpath expr="//div[@class='paymentlines']" position="replace">-->
<!--            &lt;!&ndash; Saudi Arabian Payment Section &ndash;&gt;-->
<!--            <div class="sa-payment-section">-->
<!--                <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>-->

<!--                &lt;!&ndash; Payment Methods Header &ndash;&gt;-->
<!--                <div class="sa-payment-header">-->
<!--                    <span class="sa-label-ar">طرق الدفع:</span>-->
<!--                    <span class="sa-label-en">Payment Methods:</span>-->
<!--                </div>-->

<!--                &lt;!&ndash; Payment Lines &ndash;&gt;-->
<!--                <div class="sa-payment-lines">-->
<!--                    <t t-foreach="props.data.paymentlines" t-as="line" t-key="line_index">-->
<!--                        <div class="sa-payment-row">-->
<!--                            <div class="sa-payment-method">-->
<!--                                &lt;!&ndash; Cash Payment &ndash;&gt;-->
<!--                                <t t-if="line.name.toLowerCase().includes('cash') or line.name.toLowerCase().includes('نقد')">-->
<!--                                    <span class="sa-method-ar">نقداً:</span>-->
<!--                                    <span class="sa-method-en">Cash:</span>-->
<!--                                </t>-->
<!--                                &lt;!&ndash; Card Payment &ndash;&gt;-->
<!--                                <t t-elif="line.name.toLowerCase().includes('card') or line.name.toLowerCase().includes('بطاقة')">-->
<!--                                    <span class="sa-method-ar">بطاقة:</span>-->
<!--                                    <span class="sa-method-en">Card:</span>-->
<!--                                </t>-->
<!--                                &lt;!&ndash; Other Payment Methods &ndash;&gt;-->
<!--                                <t t-else="">-->
<!--                                    <span class="sa-method-name" t-esc="line.name"/>:-->
<!--                                </t>-->
<!--                            </div>-->
<!--                            <div class="sa-payment-amount">-->
<!--                                <span class="sa-currency">ر.س</span>-->
<!--                                <span class="sa-amount" t-esc="props.formatCurrency(line.amount, false)"/>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </t>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Replace change section with Saudi Arabian format &ndash;&gt;-->
<!--        <xpath expr="//div[@class='pos-receipt-amount receipt-change mt-2']" position="replace">-->
<!--            <div class="sa-change-section" t-if="props.data.change > 0">-->
<!--                <div class="sa-change-row">-->
<!--                    <div class="sa-change-labels">-->
<!--                        <span class="sa-label-ar">الباقي:</span>-->
<!--                        <span class="sa-label-en">Change:</span>-->
<!--                    </div>-->
<!--                    <div class="sa-change-amount">-->
<!--                        <span class="sa-currency">ر.س</span>-->
<!--                        <span class="sa-amount" t-esc="props.formatCurrency(props.data.change, false)"/>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Replace footer with Saudi Arabian format &ndash;&gt;-->
<!--        <xpath expr="//div[@class='pos-receipt-order-data'][last()]" position="before">-->
<!--            &lt;!&ndash; Saudi Arabian Footer Section &ndash;&gt;-->
<!--            <div class="sa-footer-section">-->
<!--                <div class="sa-section-separator">= = = = = = = = = = = = = = = = = = = = = =</div>-->

<!--                &lt;!&ndash; QR Code Section &ndash;&gt;-->
<!--                <div class="sa-qr-section" t-if="props.data.pos_qr_code">-->
<!--                    <div class="sa-qr-label">-->
<!--                        <span class="sa-label-ar">امسح الرمز للحصول على الفاتورة الإلكترونية</span>-->
<!--                        <span class="sa-label-en">Scan for E-Invoice</span>-->
<!--                    </div>-->
<!--                    <div class="sa-qr-container">-->
<!--                        <img id="posqrcode" t-att-src="props.data.pos_qr_code" class="sa-qr-code"/>-->
<!--                    </div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Thank You Message &ndash;&gt;-->
<!--                <div class="sa-thank-you-section">-->
<!--                    <div class="sa-thank-you-message">-->
<!--                        <span class="sa-thank-you-ar">شكراً لزيارتكم</span>-->
<!--                        <span class="sa-thank-you-en">Thank you for your visit</span>-->
<!--                    </div>-->
<!--                </div>-->

<!--                &lt;!&ndash; Store Contact Information &ndash;&gt;-->
<!--                <div class="sa-store-contact" t-if="props.data.company.phone or props.data.company.website">-->
<!--                    <div class="sa-contact-phone" t-if="props.data.company.phone">-->
<!--                        <span class="sa-contact-value" t-esc="props.data.company.phone"/>-->
<!--                    </div>-->
<!--                    <div class="sa-contact-website" t-if="props.data.company.website">-->
<!--                        <span class="sa-contact-value" t-esc="props.data.company.website"/>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Remove the original "Powered by Odoo" section &ndash;&gt;-->
<!--        <xpath expr="//div[@class='pos-receipt-order-data'][last()]" position="replace">-->
<!--            &lt;!&ndash; This section is removed to maintain clean Saudi Arabian format &ndash;&gt;-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Remove other default sections that don't fit Saudi format &ndash;&gt;-->
<!--        <xpath expr="//div[@class='before-footer']" position="replace">-->
<!--            &lt;!&ndash; Removed for clean Saudi format &ndash;&gt;-->
<!--        </xpath>-->

<!--        <xpath expr="//div[@class='after-footer']" position="replace">-->
<!--            &lt;!&ndash; Payment terminal receipts can be added here if needed &ndash;&gt;-->
<!--            <div class="sa-payment-terminal-section">-->
<!--                <t t-foreach="props.data.paymentlines" t-as="line" t-key="line_index">-->
<!--                    <t t-if="line.ticket">-->
<!--                        <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>-->
<!--                        <div class="sa-payment-terminal-receipt">-->
<!--                            <pre t-esc="line.ticket" />-->
<!--                        </div>-->
<!--                    </t>-->
<!--                </t>-->
<!--            </div>-->
<!--        </xpath>-->

<!--        &lt;!&ndash; Remove shipping date section &ndash;&gt;-->
<!--        <xpath expr="//t[@t-if='props.data.shippingDate']" position="replace">-->
<!--            &lt;!&ndash; Removed for Saudi format &ndash;&gt;-->
<!--        </xpath>-->

<!--    </t>-->
<!--</templates>-->


<templates id="template" xml:space="preserve">
    <t t-name="PosDiscountButton">
        <span class="control-button btn btn-light rounded-0 fw-bolder" t-on-click="() => this.onClick()">
             <i class="fa fa-gift"> </i>
            <span>5%</span>
            <span>Discount</span>
        </span>
    </t>
    <t t-name="PosLastOrderButton">
        <span class="control-button btn btn-light rounded-0 fw-bolder" t-on-click="() => this.onClick()">
            <i class="fa fa-shopping-cart"></i>
            Making RPC calls
            <span></span>
            <span>Last Orders</span>
        </span>
    </t>


    <t t-name="ProductsWidget"
       t-inherit="point_of_sale.ProductsWidget"
       t-inherit-mode="extension">
        <xpath expr="//ProductCard" position="attributes">
            <attribute name="standard_price">
                pos.env.utils.formatCurrency(product.get_display_price() -
                product.standard_price)
            </attribute>
        </xpath>
    </t>
    <t t-name="ProductCard" t-inherit="point_of_sale.ProductCard" t-inherit-mode="extension">
        <xpath expr="//span[hasclass('price-tag')]" position="after">
            <span t-if="props.standard_price" class="sale_margin py-1 fw-bolder">
                <t t-esc="props.standard_price"/>
            </span>
        </xpath>
    </t>

    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('pos-receipt')]//div[hasclass('before-footer')]" position="before">
            <div style="text-align:center;" t-if="props.data.saved_amount">
            <br/>
                <div>
                You saved
                <t t-esc="props.data.saved_amount"/>
                    on this order.
                </div>
            </div>
        </xpath>

        <!-- Wrap the entire receipt in Saudi Arabian thermal receipt container -->
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="class">pos-receipt sa-thermal-receipt</attribute>
        </xpath>

        <!-- Add Invoice Information Section after header -->
        <xpath expr="//ReceiptHeader" position="after">
            <!-- Invoice Type Section -->
            <div class="sa-invoice-type-section">
                <div class="sa-invoice-type">
                    <span class="sa-invoice-type-ar">فاتورة ضريبية مبسطة</span>
                    <span class="sa-invoice-type-en">Simplified Tax Invoice</span>
                </div>
            </div>

            <!-- Invoice Information Section -->
            <div class="sa-invoice-info-section">
                <!-- Invoice Number -->
                <div class="sa-info-row sa-invoice-number">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">رقم الفاتورة:</span>
                        <span class="sa-label-en">Invoice No:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.name"/>
                </div>

                <!-- Date and Time -->
                <div class="sa-info-row sa-datetime">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">التاريخ والوقت:</span>
                        <span class="sa-label-en">Date &amp; Time:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.date"/>
                </div>

                <!-- Cashier Information -->
                <div class="sa-info-row sa-cashier" t-if="props.data.cashier">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">أمين الصندوق:</span>
                        <span class="sa-label-en">Cashier:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.cashier"/>
                </div>
            </div>

            <!-- Customer Information Section (conditional) -->
            <div class="sa-customer-section" t-if="props.data.client">
                <div class="sa-section-separator">- - - - - - - - - - - - - - - - - - - - - -</div>

                <!-- Customer Name -->
                <div class="sa-info-row sa-customer-name">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">اسم العميل:</span>
                        <span class="sa-label-en">Customer:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.client"/>
                </div>

                <!-- Customer VAT (if available) -->
                <div class="sa-info-row sa-customer-vat" t-if="props.data.client_vat">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">الرقم الضريبي للعميل:</span>
                        <span class="sa-label-en">Customer VAT:</span>
                    </div>
                    <div class="sa-info-value" t-esc="props.data.client_vat"/>
                </div>
            </div>
        </xpath>





    </t>
</templates>