.pos-receipt-container {
    direction: ltr;
}

.receipt-screen .default-view .pos-receipt-container {
    flex-basis: 400px;
}

@page {
    padding: 15px;
}

@media print {
    body {
        background-color: transparent;
    }
    body * {
        visibility: hidden;
    }
    .pos, .pos * {
        position: static !important;
    }
    .render-container .pos-receipt * {
        visibility: visible;
        background-color: transparent !important;
        color: black !important;
    }
    .render-container .pos-receipt {
        margin: 0 !important;
        margin-left: auto !important;
        margin-right: auto !important;
        border: none !important;
        font-size: 14px !important;
        width: 266px !important;
    }
    .render-container {
        position: absolute !important;
        top: 10px;
        left: 10px;
    }
    .o-main-components-container  {
        position: absolute !important;
        top: 0;
        left: 0;
    }
}
